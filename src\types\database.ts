export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          full_name: string | null
          role: 'admin' | 'sales_person' | 'user'
          avatar_url: string | null
          phone: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          email: string
          full_name?: string | null
          role?: 'admin' | 'sales_person' | 'user'
          avatar_url?: string | null
          phone?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          role?: 'admin' | 'sales_person' | 'user'
          avatar_url?: string | null
          phone?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      properties: {
        Row: {
          id: string
          title: string
          description: string | null
          price: number
          currency: string
          property_type: 'apartment' | 'house' | 'villa' | 'commercial' | 'land'
          status: 'available' | 'sold' | 'rented' | 'pending'
          bedrooms: number | null
          bathrooms: number | null
          area: number | null
          location: Json | null
          address: string | null
          city: string | null
          region: string | null
          country: string
          features: string[] | null
          images: string[] | null
          videos: string[] | null
          virtual_tour_url: string | null
          created_by: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          description?: string | null
          price: number
          currency?: string
          property_type: 'apartment' | 'house' | 'villa' | 'commercial' | 'land'
          status?: 'available' | 'sold' | 'rented' | 'pending'
          bedrooms?: number | null
          bathrooms?: number | null
          area?: number | null
          location?: Json | null
          address?: string | null
          city?: string | null
          region?: string | null
          country?: string
          features?: string[] | null
          images?: string[] | null
          videos?: string[] | null
          virtual_tour_url?: string | null
          created_by: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          description?: string | null
          price?: number
          currency?: string
          property_type?: 'apartment' | 'house' | 'villa' | 'commercial' | 'land'
          status?: 'available' | 'sold' | 'rented' | 'pending'
          bedrooms?: number | null
          bathrooms?: number | null
          area?: number | null
          location?: Json | null
          address?: string | null
          city?: string | null
          region?: string | null
          country?: string
          features?: string[] | null
          images?: string[] | null
          videos?: string[] | null
          virtual_tour_url?: string | null
          created_by?: string
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "properties_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      property_inquiries: {
        Row: {
          id: string
          property_id: string
          user_id: string | null
          name: string
          email: string
          phone: string | null
          message: string | null
          inquiry_type: 'purchase' | 'rent' | 'information'
          status: 'new' | 'contacted' | 'scheduled' | 'closed'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          property_id: string
          user_id?: string | null
          name: string
          email: string
          phone?: string | null
          message?: string | null
          inquiry_type: 'purchase' | 'rent' | 'information'
          status?: 'new' | 'contacted' | 'scheduled' | 'closed'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          property_id?: string
          user_id?: string | null
          name?: string
          email?: string
          phone?: string | null
          message?: string | null
          inquiry_type?: 'purchase' | 'rent' | 'information'
          status?: 'new' | 'contacted' | 'scheduled' | 'closed'
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "property_inquiries_property_id_fkey"
            columns: ["property_id"]
            isOneToOne: false
            referencedRelation: "properties"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "property_inquiries_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      blog_posts: {
        Row: {
          id: string
          title: string
          slug: string
          content: string | null
          excerpt: string | null
          featured_image: string | null
          category: string | null
          tags: string[] | null
          published: boolean
          author_id: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          slug: string
          content?: string | null
          excerpt?: string | null
          featured_image?: string | null
          category?: string | null
          tags?: string[] | null
          published?: boolean
          author_id: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          slug?: string
          content?: string | null
          excerpt?: string | null
          featured_image?: string | null
          category?: string | null
          tags?: string[] | null
          published?: boolean
          author_id?: string
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "blog_posts_author_id_fkey"
            columns: ["author_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      contact_submissions: {
        Row: {
          id: string
          name: string
          email: string
          phone: string | null
          subject: string
          message: string
          inquiry_type: 'general' | 'buying' | 'selling' | 'renting' | 'management' | 'investment'
          status: 'new' | 'contacted' | 'resolved' | 'closed'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          email: string
          phone?: string | null
          subject: string
          message: string
          inquiry_type: 'general' | 'buying' | 'selling' | 'renting' | 'management' | 'investment'
          status?: 'new' | 'contacted' | 'resolved' | 'closed'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          email?: string
          phone?: string | null
          subject?: string
          message?: string
          inquiry_type?: 'general' | 'buying' | 'selling' | 'renting' | 'management' | 'investment'
          status?: 'new' | 'contacted' | 'resolved' | 'closed'
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
