import { useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Users,
  Building,
  MessageSquare,
  FileText,
  TrendingUp,
  Activity,
  Eye,
  Loader2
} from 'lucide-react';
import { UserService } from '@/lib/services/user-service';
import { PropertyService } from '@/lib/services/property-service';
import { InquiryService } from '@/lib/services/inquiry-service';
import { ContactService } from '@/lib/services/contact-service';

interface DashboardStats {
  totalUsers: number;
  newUsersThisMonth: number;
  totalProperties: number;
  totalInquiries: number;
  newInquiries: number;
  totalContacts: number;
  newContacts: number;
}

const recentActivity = [
  {
    type: 'user_registered',
    message: 'New user <PERSON> registered',
    time: '2 minutes ago',
  },
  {
    type: 'property_added',
    message: 'New property added in Casablanca',
    time: '15 minutes ago',
  },
  {
    type: 'inquiry_received',
    message: 'New inquiry for Villa in Marrakech',
    time: '1 hour ago',
  },
  {
    type: 'blog_published',
    message: 'Blog post "Market Trends 2024" published',
    time: '2 hours ago',
  },
];

const topProperties = [
  {
    title: 'Luxury Villa in Casablanca',
    views: 234,
    inquiries: 12,
    price: '2,500,000 MAD',
  },
  {
    title: 'Modern Apartment in Rabat',
    views: 189,
    inquiries: 8,
    price: '850,000 MAD',
  },
  {
    title: 'Traditional Riad in Marrakech',
    views: 156,
    inquiries: 15,
    price: '1,200,000 MAD',
  },
];

export function AdminDashboard() {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardStats();
  }, []);

  const loadDashboardStats = async () => {
    try {
      setLoading(true);

      // Load all stats in parallel
      const [userStats, propertyStats, inquiryStats, contactStats] = await Promise.all([
        UserService.getUserStats(),
        PropertyService.searchProperties({}, { limit: 1 }), // Just get count
        InquiryService.getInquiryStats(),
        ContactService.getContactStats(),
      ]);

      setStats({
        totalUsers: userStats.total,
        newUsersThisMonth: userStats.newThisMonth,
        totalProperties: propertyStats.total,
        totalInquiries: inquiryStats.total,
        newInquiries: inquiryStats.newInquiries,
        totalContacts: contactStats.total,
        newContacts: contactStats.newContacts,
      });
    } catch (error) {
      console.error('Error loading dashboard stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const dashboardStats = stats ? [
    {
      title: 'Total Users',
      value: stats.totalUsers.toLocaleString(),
      change: `+${stats.newUsersThisMonth} this month`,
      icon: Users,
      color: 'text-blue-600',
    },
    {
      title: 'Total Properties',
      value: stats.totalProperties.toLocaleString(),
      change: 'Active listings',
      icon: Building,
      color: 'text-green-600',
    },
    {
      title: 'Active Inquiries',
      value: stats.newInquiries.toLocaleString(),
      change: `${stats.totalInquiries} total`,
      icon: MessageSquare,
      color: 'text-purple-600',
    },
    {
      title: 'Contact Submissions',
      value: stats.newContacts.toLocaleString(),
      change: `${stats.totalContacts} total`,
      icon: FileText,
      color: 'text-orange-600',
    },
  ] : [];

  return (
    <div className="space-y-10 min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100">
      <div className="relative">
        <div className="absolute inset-0 bg-gradient-to-r from-italian-green/5 via-white/50 to-italian-red/5 rounded-2xl -z-10"></div>
        <div className="relative py-8">
          <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 italian-flag-corner animate-slide-up mb-3">
            Admin Dashboard
          </h1>
          <p className="text-xl text-gray-600 animate-fade-in-delay">
            Overview of system performance and key metrics.
          </p>
        </div>
      </div>

      {/* Stats Grid */}
      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {[1, 2, 3, 4].map((index) => (
            <Card key={index} className="bg-gradient-to-br from-white to-gray-50 border-0 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-italian-green" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {dashboardStats.map((stat, index) => {
          const Icon = stat.icon;
          const gradientColors = [
            'from-blue-500 to-blue-600',
            'from-italian-green to-green-600',
            'from-purple-500 to-purple-600',
            'from-italian-red to-red-600'
          ];
          return (
            <Card key={index} className="hover:shadow-2xl transition-all duration-500 hover-lift group bg-gradient-to-br from-white to-gray-50 border-0 shadow-lg animate-scale-in" style={{ animationDelay: `${index * 100}ms` }}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                <CardTitle className="text-sm font-semibold text-gray-600 group-hover:text-gray-800 transition-colors duration-300">
                  {stat.title}
                </CardTitle>
                <div className={`w-12 h-12 bg-gradient-to-br ${gradientColors[index]} rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                  <Icon className="h-6 w-6 text-white" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-gray-900 mb-2 group-hover:text-italian-green transition-colors duration-300">
                  {stat.value}
                </div>
                <p className="text-sm text-gray-600 font-medium">{stat.change}</p>
              </CardContent>
            </Card>
          );
        })}
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-10">
        {/* Recent Activity */}
        <Card className="bg-gradient-to-br from-white to-gray-50 border-0 shadow-xl hover:shadow-2xl transition-all duration-500 hover-lift">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center text-xl font-bold text-gray-900 italian-flag-corner">
              <div className="w-10 h-10 bg-gradient-to-br from-italian-green to-italian-red rounded-xl flex items-center justify-center mr-3 shadow-lg">
                <Activity className="h-5 w-5 text-white" />
              </div>
              Recent Activity
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-5">
              {recentActivity.map((activity, index) => (
                <div key={index} className="flex items-start space-x-4 p-4 bg-gradient-to-r from-gray-50 to-white rounded-xl hover:shadow-md transition-all duration-300 animate-fade-in" style={{ animationDelay: `${index * 100}ms` }}>
                  <div className="w-3 h-3 bg-gradient-to-r from-italian-green to-italian-red rounded-full mt-2 flex-shrink-0"></div>
                  <div className="flex-1">
                    <p className="text-gray-900 font-medium leading-relaxed">{activity.message}</p>
                    <p className="text-sm text-gray-500 mt-1 font-medium">{activity.time}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Top Properties */}
        <Card className="bg-gradient-to-br from-white to-gray-50 border-0 shadow-xl hover:shadow-2xl transition-all duration-500 hover-lift">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center text-xl font-bold text-gray-900 italian-flag-corner">
              <div className="w-10 h-10 bg-gradient-to-br from-italian-green to-italian-red rounded-xl flex items-center justify-center mr-3 shadow-lg">
                <TrendingUp className="h-5 w-5 text-white" />
              </div>
              Top Performing Properties
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {topProperties.map((property, index) => (
                <div key={index} className="flex items-center justify-between p-5 bg-gradient-to-r from-gray-50 to-white rounded-xl hover:shadow-lg transition-all duration-300 hover-lift group animate-scale-in" style={{ animationDelay: `${index * 100}ms` }}>
                  <div className="flex-1">
                    <h4 className="font-bold text-gray-900 mb-2 group-hover:text-italian-green transition-colors duration-300">
                      {property.title}
                    </h4>
                    <div className="flex items-center space-x-6 text-sm text-gray-600">
                      <div className="flex items-center bg-blue-50 px-3 py-1 rounded-full">
                        <Eye className="h-4 w-4 mr-2 text-blue-600" />
                        <span className="font-medium">{property.views} views</span>
                      </div>
                      <div className="flex items-center bg-green-50 px-3 py-1 rounded-full">
                        <MessageSquare className="h-4 w-4 mr-2 text-green-600" />
                        <span className="font-medium">{property.inquiries} inquiries</span>
                      </div>
                    </div>
                  </div>
                  <div className="text-right ml-4">
                    <div className="font-bold text-lg text-gray-900 bg-gradient-to-r from-italian-green to-italian-red bg-clip-text text-transparent">
                      {property.price}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card className="bg-gradient-to-br from-white to-gray-50 border-0 shadow-xl hover:shadow-2xl transition-all duration-500 hover-lift">
        <CardHeader className="pb-4">
          <CardTitle className="text-2xl font-bold text-gray-900 italian-flag-corner">
            Quick Actions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <button className="p-6 bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl hover:shadow-xl transition-all duration-500 hover-lift group border-0 animate-scale-in">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                <Users className="h-6 w-6 text-white" />
              </div>
              <div className="text-sm font-bold text-gray-900 group-hover:text-blue-600 transition-colors duration-300">Manage Users</div>
            </button>
            <button className="p-6 bg-gradient-to-br from-green-50 to-green-100 rounded-2xl hover:shadow-xl transition-all duration-500 hover-lift group border-0 animate-scale-in" style={{ animationDelay: '100ms' }}>
              <div className="w-12 h-12 bg-gradient-to-br from-italian-green to-green-600 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                <Building className="h-6 w-6 text-white" />
              </div>
              <div className="text-sm font-bold text-gray-900 group-hover:text-italian-green transition-colors duration-300">Add Property</div>
            </button>
            <button className="p-6 bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl hover:shadow-xl transition-all duration-500 hover-lift group border-0 animate-scale-in" style={{ animationDelay: '200ms' }}>
              <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                <FileText className="h-6 w-6 text-white" />
              </div>
              <div className="text-sm font-bold text-gray-900 group-hover:text-purple-600 transition-colors duration-300">Create Blog Post</div>
            </button>
            <button className="p-6 bg-gradient-to-br from-red-50 to-red-100 rounded-2xl hover:shadow-xl transition-all duration-500 hover-lift group border-0 animate-scale-in" style={{ animationDelay: '300ms' }}>
              <div className="w-12 h-12 bg-gradient-to-br from-italian-red to-red-600 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                <MessageSquare className="h-6 w-6 text-white" />
              </div>
              <div className="text-sm font-bold text-gray-900 group-hover:text-italian-red transition-colors duration-300">View Inquiries</div>
            </button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
