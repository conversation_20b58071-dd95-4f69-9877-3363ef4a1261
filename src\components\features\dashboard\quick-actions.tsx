import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Plus, Upload, FileText, Users, ArrowRight, Sparkles, Zap } from 'lucide-react';
import Link from 'next/link';

const actions = [
  {
    title: 'Add Property',
    description: 'List a new property',
    icon: Plus,
    href: '/dashboard/properties/new',
    gradient: 'from-blue-500 to-blue-600',
    bgGradient: 'from-blue-50 to-blue-100',
    hoverGradient: 'hover:from-blue-600 hover:to-blue-700',
    category: 'Property'
  },
  {
    title: 'Upload Images',
    description: 'Add property photos',
    icon: Upload,
    href: '/dashboard/media',
    gradient: 'from-italian-green to-green-600',
    bgGradient: 'from-green-50 to-green-100',
    hoverGradient: 'hover:from-italian-green hover:to-green-700',
    category: 'Media'
  },
  {
    title: 'Create Blog Post',
    description: 'Write a new article',
    icon: FileText,
    href: '/admin/blog/new',
    gradient: 'from-purple-500 to-purple-600',
    bgGradient: 'from-purple-50 to-purple-100',
    hoverGradient: 'hover:from-purple-600 hover:to-purple-700',
    category: 'Content'
  },
  {
    title: 'Manage Users',
    description: 'User administration',
    icon: Users,
    href: '/admin/users',
    gradient: 'from-italian-red to-red-600',
    bgGradient: 'from-red-50 to-red-100',
    hoverGradient: 'hover:from-italian-red hover:to-red-700',
    category: 'Admin'
  },
];

export function QuickActions() {
  return (
    <Card className="bg-gradient-to-br from-white to-gray-50 border-0 shadow-xl hover:shadow-2xl transition-all duration-500 hover-lift relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-italian-green/10 to-italian-red/10 rounded-full -translate-y-16 translate-x-16"></div>

      <CardHeader className="relative z-10 pb-6">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-2xl font-bold text-gray-900 italian-flag-corner mb-2">
              Quick Actions
            </CardTitle>
            <p className="text-gray-600 font-medium">
              Streamline your workflow
            </p>
          </div>
          <div className="w-12 h-12 bg-gradient-to-br from-italian-green to-italian-red rounded-2xl flex items-center justify-center shadow-lg">
            <Zap className="h-6 w-6 text-white" />
          </div>
        </div>
      </CardHeader>

      <CardContent className="relative z-10 space-y-4">
        {actions.map((action, index) => {
          const Icon = action.icon;
          return (
            <Link key={index} href={action.href} className="block group">
              <div className={`relative overflow-hidden bg-gradient-to-br ${action.bgGradient} rounded-2xl p-6 transition-all duration-500 hover:shadow-xl hover:scale-105 border-0 animate-fade-in`} style={{ animationDelay: `${index * 100}ms` }}>
                {/* Background pattern */}
                <div className="absolute inset-0 bg-gradient-to-br from-white/50 to-transparent"></div>
                <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-white/20 to-transparent rounded-full -translate-y-10 translate-x-10"></div>

                <div className="relative z-10 flex items-center space-x-4">
                  <div className={`w-16 h-16 bg-gradient-to-br ${action.gradient} rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 group-hover:shadow-xl transition-all duration-300`}>
                    <Icon className="h-8 w-8 text-white" />
                  </div>

                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-xs font-bold text-gray-500 uppercase tracking-wider">
                        {action.category}
                      </span>
                      <ArrowRight className="h-5 w-5 text-gray-400 group-hover:text-italian-green group-hover:translate-x-1 transition-all duration-300" />
                    </div>
                    <h3 className="font-bold text-lg text-gray-900 group-hover:text-italian-green transition-colors duration-300 mb-1">
                      {action.title}
                    </h3>
                    <p className="text-gray-600 text-sm font-medium">
                      {action.description}
                    </p>
                  </div>
                </div>

                {/* Hover effect overlay */}
                <div className="absolute inset-0 bg-gradient-to-r from-italian-green/5 to-italian-red/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl"></div>
              </div>
            </Link>
          );
        })}

        {/* Footer */}
        <div className="mt-8 pt-6 border-t border-gray-200">
          <div className="flex items-center justify-center space-x-2 text-gray-500">
            <Sparkles className="h-4 w-4" />
            <span className="text-sm font-medium">More actions coming soon</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
