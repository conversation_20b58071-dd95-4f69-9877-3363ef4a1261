'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Search,
  Filter,
  Plus,
  Edit,
  Trash2,
  Eye,
  MapPin,
  Bed,
  Bath,
  Square,
  DollarSign,
  Loader2,
  MoreHorizontal
} from 'lucide-react';
import { PropertyService } from '@/lib/services/property-service';
import type { Database } from '@/types/database';
import Link from 'next/link';

type Property = Database['public']['Tables']['properties']['Row'];

export function PropertyManagement() {
  const [properties, setProperties] = useState<Property[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<'all' | 'available' | 'sold' | 'rented' | 'pending'>('all');
  const [selectedType, setSelectedType] = useState<'all' | 'apartment' | 'house' | 'villa' | 'commercial' | 'land'>('all');
  const [total, setTotal] = useState(0);

  // Load properties from Supabase
  useEffect(() => {
    loadProperties();
  }, [searchQuery, selectedStatus, selectedType]);

  const loadProperties = async () => {
    try {
      setLoading(true);
      const { properties: fetchedProperties, total: totalCount } = await PropertyService.searchProperties(
        {
          searchQuery: searchQuery || undefined,
          status: selectedStatus === 'all' ? undefined : selectedStatus,
          propertyType: selectedType === 'all' ? undefined : selectedType,
        },
        {
          limit: 50,
          offset: 0,
        }
      );
      setProperties(fetchedProperties);
      setTotal(totalCount);
    } catch (error) {
      console.error('Error loading properties:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string): 'success' | 'warning' | 'destructive' | 'secondary' => {
    switch (status) {
      case 'available': return 'success';
      case 'pending': return 'warning';
      case 'sold': return 'destructive';
      case 'rented': return 'secondary';
      default: return 'secondary';
    }
  };

  const formatPrice = (price: number, currency: string = 'MAD') => {
    return new Intl.NumberFormat('en-US').format(price) + ' ' + currency;
  };

  return (
    <div className="space-y-8 min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100">
      {/* Header Actions */}
      <div className="bg-gradient-to-r from-white to-gray-50 rounded-2xl p-6 shadow-lg border-0">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
          <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-4 sm:space-y-0 sm:space-x-6">
            <div className="relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-italian-green h-5 w-5" />
              <Input
                placeholder="Search properties..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-12 w-80 h-12 border-2 border-gray-200 focus:border-italian-green rounded-xl bg-white shadow-sm"
              />
            </div>

            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value as any)}
              className="border-2 border-gray-200 focus:border-italian-green rounded-xl px-4 py-3 h-12 bg-white shadow-sm font-medium text-gray-700"
            >
              <option value="all">All Status</option>
              <option value="available">Available</option>
              <option value="pending">Pending</option>
              <option value="sold">Sold</option>
              <option value="rented">Rented</option>
            </select>

            <select
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value as any)}
              className="border-2 border-gray-200 focus:border-italian-green rounded-xl px-4 py-3 h-12 bg-white shadow-sm font-medium text-gray-700"
            >
              <option value="all">All Types</option>
              <option value="apartment">Apartment</option>
              <option value="house">House</option>
              <option value="villa">Villa</option>
              <option value="commercial">Commercial</option>
              <option value="land">Land</option>
            </select>

            <Button variant="outline" className="h-12 px-6 border-2 border-gray-200 hover:border-italian-green hover:bg-italian-green hover:text-white transition-all duration-300 rounded-xl">
              <Filter className="mr-2 h-5 w-5" />
              More Filters
            </Button>
          </div>

          <Link href="/dashboard/properties/new">
            <Button className="h-12 px-8 bg-gradient-to-r from-italian-green to-italian-red hover:from-italian-red hover:to-italian-green transition-all duration-300 border-0 rounded-xl shadow-lg hover:shadow-xl font-semibold">
              <Plus className="mr-3 h-5 w-5" />
              Add Property
            </Button>
          </Link>
        </div>
      </div>

      {/* Properties Grid */}
      <Card className="bg-gradient-to-br from-white to-gray-50 border-0 shadow-xl hover:shadow-2xl transition-all duration-500">
        <CardHeader className="pb-4">
          <CardTitle className="text-2xl font-bold text-gray-900 italian-flag-corner">
            Properties ({total})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-italian-green" />
              <span className="ml-2 text-gray-600">Loading properties...</span>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {properties.map((property, index) => (
                <Card key={property.id} className="hover:shadow-lg transition-all duration-300 animate-fade-in" style={{ animationDelay: `${index * 100}ms` }}>
                  <div className="relative">
                    {property.images && property.images.length > 0 ? (
                      <img
                        src={property.images[0]}
                        alt={property.title}
                        className="w-full h-48 object-cover rounded-t-lg"
                      />
                    ) : (
                      <div className="w-full h-48 bg-gradient-to-br from-gray-200 to-gray-300 rounded-t-lg flex items-center justify-center">
                        <span className="text-gray-500">No Image</span>
                      </div>
                    )}
                    <div className="absolute top-3 right-3">
                      <Badge variant={getStatusColor(property.status)} className="px-2 py-1 text-xs font-semibold">
                        {property.status}
                      </Badge>
                    </div>
                  </div>
                  
                  <CardContent className="p-4">
                    <div className="space-y-3">
                      <div>
                        <h3 className="font-bold text-lg text-gray-900 line-clamp-1">{property.title}</h3>
                        <div className="flex items-center text-sm text-gray-600 mt-1">
                          <MapPin className="h-4 w-4 mr-1 text-italian-green" />
                          {property.city}, {property.region}
                        </div>
                      </div>

                      <div className="flex items-center justify-between text-sm text-gray-600">
                        {property.bedrooms && (
                          <div className="flex items-center">
                            <Bed className="h-4 w-4 mr-1" />
                            {property.bedrooms}
                          </div>
                        )}
                        {property.bathrooms && (
                          <div className="flex items-center">
                            <Bath className="h-4 w-4 mr-1" />
                            {property.bathrooms}
                          </div>
                        )}
                        {property.area && (
                          <div className="flex items-center">
                            <Square className="h-4 w-4 mr-1" />
                            {property.area}m²
                          </div>
                        )}
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center text-lg font-bold text-italian-green">
                          <DollarSign className="h-5 w-5 mr-1" />
                          {formatPrice(property.price, property.currency)}
                        </div>
                        <Badge variant="outline" className="text-xs">
                          {property.property_type}
                        </Badge>
                      </div>

                      <div className="flex items-center space-x-2 pt-2">
                        <Button variant="outline" size="sm" className="flex-1 hover:bg-italian-green hover:text-white transition-all duration-300">
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Button>
                        <Button variant="outline" size="sm" className="hover:bg-blue-500 hover:text-white transition-all duration-300">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm" className="hover:bg-red-500 hover:text-white transition-all duration-300">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm" className="hover:bg-gray-500 hover:text-white transition-all duration-300">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
