import { PropertyManagement } from '@/components/features/admin/property-management';

export default function AdminPropertiesPage() {
  return (
    <div className="space-y-10">
      <div className="relative">
        <div className="absolute inset-0 bg-gradient-to-r from-italian-green/5 via-white/50 to-italian-red/5 rounded-2xl -z-10"></div>
        <div className="relative py-8">
          <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 italian-flag-corner animate-slide-up mb-3">
            Property Management
          </h1>
          <p className="text-xl text-gray-600 animate-fade-in-delay">
            Manage all properties, listings, and property-related content.
          </p>
        </div>
      </div>

      <PropertyManagement />
    </div>
  );
}
