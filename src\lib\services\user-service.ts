import { supabase } from '@/lib/supabase/client';
import { createClient } from '@supabase/supabase-js';
import type { Database } from '@/types/database';

type User = Database['public']['Tables']['users']['Row'];
type UserInsert = Database['public']['Tables']['users']['Insert'];
type UserUpdate = Database['public']['Tables']['users']['Update'];

// Create admin client for admin operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export interface UserSearchFilters {
  searchQuery?: string;
  role?: 'admin' | 'sales_person' | 'user' | 'all';
  status?: 'active' | 'inactive' | 'all';
}

export interface UserSearchOptions {
  limit?: number;
  offset?: number;
  sortBy?: keyof User;
  sortOrder?: 'asc' | 'desc';
}

export class UserService {
  static async getUsers(
    filters: UserSearchFilters = {},
    options: UserSearchOptions = {}
  ) {
    const {
      searchQuery,
      role,
      status,
    } = filters;

    const {
      limit = 20,
      offset = 0,
      sortBy = 'created_at',
      sortOrder = 'desc',
    } = options;

    let query = supabaseAdmin
      .from('users')
      .select('*', { count: 'exact' })
      .range(offset, offset + limit - 1)
      .order(sortBy, { ascending: sortOrder === 'asc' });

    // Apply filters
    if (searchQuery) {
      query = query.or(`full_name.ilike.%${searchQuery}%,email.ilike.%${searchQuery}%`);
    }

    if (role && role !== 'all') {
      query = query.eq('role', role);
    }

    // Note: status filter would need to be added to the database schema
    // For now, we'll filter client-side if needed

    const { data, error, count } = await query;

    if (error) {
      throw error;
    }

    return {
      users: data || [],
      total: count || 0,
    };
  }

  static async getUserById(id: string): Promise<User> {
    const { data, error } = await supabaseAdmin
      .from('users')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      throw error;
    }

    return data;
  }

  static async createUser(user: UserInsert): Promise<User> {
    const { data, error } = await supabaseAdmin
      .from('users')
      .insert(user)
      .select()
      .single();

    if (error) {
      throw error;
    }

    return data;
  }

  static async updateUser(id: string, updates: UserUpdate): Promise<User> {
    const { data, error } = await supabaseAdmin
      .from('users')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw error;
    }

    return data;
  }

  static async deleteUser(id: string): Promise<void> {
    const { error } = await supabaseAdmin
      .from('users')
      .delete()
      .eq('id', id);

    if (error) {
      throw error;
    }
  }

  static async updateUserRole(id: string, role: 'admin' | 'sales_person' | 'user'): Promise<User> {
    return this.updateUser(id, { role });
  }

  static async getUserStats() {
    const { data: users, error } = await supabaseAdmin
      .from('users')
      .select('role, created_at');

    if (error) {
      throw error;
    }

    const total = users?.length || 0;
    const admins = users?.filter(u => u.role === 'admin').length || 0;
    const salesPersons = users?.filter(u => u.role === 'sales_person').length || 0;
    const regularUsers = users?.filter(u => u.role === 'user').length || 0;

    // Calculate new users this month
    const thisMonth = new Date();
    thisMonth.setDate(1);
    const newThisMonth = users?.filter(u => 
      new Date(u.created_at) >= thisMonth
    ).length || 0;

    return {
      total,
      admins,
      salesPersons,
      regularUsers,
      newThisMonth,
    };
  }
}
