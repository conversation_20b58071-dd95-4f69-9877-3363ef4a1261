import { supabase } from '@/lib/supabase/client';
import { createClient } from '@supabase/supabase-js';

// Create admin client for admin operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export interface ContactSubmission {
  id?: string;
  name: string;
  email: string;
  phone?: string;
  subject: string;
  message: string;
  inquiry_type: 'general' | 'buying' | 'selling' | 'renting' | 'management' | 'investment';
  status?: 'new' | 'contacted' | 'resolved' | 'closed';
  created_at?: string;
  updated_at?: string;
}

export interface ContactSearchFilters {
  searchQuery?: string;
  inquiryType?: string;
  status?: string;
  dateFrom?: string;
  dateTo?: string;
}

export interface ContactSearchOptions {
  limit?: number;
  offset?: number;
  sortBy?: keyof ContactSubmission;
  sortOrder?: 'asc' | 'desc';
}

export class ContactService {
  static async submitContact(contact: Omit<ContactSubmission, 'id' | 'created_at' | 'updated_at'>): Promise<ContactSubmission> {
    const { data, error } = await supabase
      .from('contact_submissions')
      .insert({
        ...contact,
        status: 'new',
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    return data;
  }

  static async getContacts(
    filters: ContactSearchFilters = {},
    options: ContactSearchOptions = {}
  ) {
    const {
      searchQuery,
      inquiryType,
      status,
      dateFrom,
      dateTo,
    } = filters;

    const {
      limit = 20,
      offset = 0,
      sortBy = 'created_at',
      sortOrder = 'desc',
    } = options;

    let query = supabaseAdmin
      .from('contact_submissions')
      .select('*', { count: 'exact' })
      .range(offset, offset + limit - 1)
      .order(sortBy, { ascending: sortOrder === 'asc' });

    // Apply filters
    if (searchQuery) {
      query = query.or(`name.ilike.%${searchQuery}%,email.ilike.%${searchQuery}%,subject.ilike.%${searchQuery}%`);
    }

    if (inquiryType && inquiryType !== 'all') {
      query = query.eq('inquiry_type', inquiryType);
    }

    if (status && status !== 'all') {
      query = query.eq('status', status);
    }

    if (dateFrom) {
      query = query.gte('created_at', dateFrom);
    }

    if (dateTo) {
      query = query.lte('created_at', dateTo);
    }

    const { data, error, count } = await query;

    if (error) {
      throw error;
    }

    return {
      contacts: data || [],
      total: count || 0,
    };
  }

  static async getContactById(id: string): Promise<ContactSubmission> {
    const { data, error } = await supabaseAdmin
      .from('contact_submissions')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      throw error;
    }

    return data;
  }

  static async updateContactStatus(id: string, status: 'new' | 'contacted' | 'resolved' | 'closed'): Promise<ContactSubmission> {
    const { data, error } = await supabaseAdmin
      .from('contact_submissions')
      .update({ 
        status,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw error;
    }

    return data;
  }

  static async deleteContact(id: string): Promise<void> {
    const { error } = await supabaseAdmin
      .from('contact_submissions')
      .delete()
      .eq('id', id);

    if (error) {
      throw error;
    }
  }

  static async getContactStats() {
    const { data: contacts, error } = await supabaseAdmin
      .from('contact_submissions')
      .select('inquiry_type, status, created_at');

    if (error) {
      throw error;
    }

    const total = contacts?.length || 0;
    const newContacts = contacts?.filter(c => c.status === 'new').length || 0;
    const resolved = contacts?.filter(c => c.status === 'resolved').length || 0;

    // Calculate contacts this month
    const thisMonth = new Date();
    thisMonth.setDate(1);
    const thisMonthCount = contacts?.filter(c => 
      new Date(c.created_at) >= thisMonth
    ).length || 0;

    // Group by inquiry type
    const byInquiryType = contacts?.reduce((acc, contact) => {
      acc[contact.inquiry_type] = (acc[contact.inquiry_type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>) || {};

    return {
      total,
      newContacts,
      resolved,
      thisMonthCount,
      byInquiryType,
    };
  }
}
