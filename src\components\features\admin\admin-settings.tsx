'use client';

import { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Settings,
  Database,
  Mail,
  Shield,
  Globe,
  Bell,
  Users,
  FileText,
  Save,
  RefreshCw,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';

export function AdminSettings() {
  const [saving, setSaving] = useState(false);
  const [saved, setSaved] = useState(false);

  const handleSave = async () => {
    setSaving(true);
    // Simulate save operation
    await new Promise(resolve => setTimeout(resolve, 1000));
    setSaving(false);
    setSaved(true);
    setTimeout(() => setSaved(false), 3000);
  };

  return (
    <div className="space-y-8 min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100">
      {/* Settings Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        
        {/* General Settings */}
        <Card className="bg-gradient-to-br from-white to-gray-50 border-0 shadow-xl hover:shadow-2xl transition-all duration-500">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center text-xl font-bold text-gray-900 italian-flag-corner">
              <div className="w-10 h-10 bg-gradient-to-br from-italian-green to-italian-red rounded-xl flex items-center justify-center mr-3 shadow-lg">
                <Settings className="h-5 w-5 text-white" />
              </div>
              General Settings
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Site Name
              </label>
              <Input
                defaultValue="Darden Property & Management"
                className="border-2 border-gray-200 focus:border-italian-green rounded-xl"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Site Description
              </label>
              <Input
                defaultValue="Premier real estate and property management services in Morocco"
                className="border-2 border-gray-200 focus:border-italian-green rounded-xl"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Contact Email
              </label>
              <Input
                defaultValue="<EMAIL>"
                type="email"
                className="border-2 border-gray-200 focus:border-italian-green rounded-xl"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Contact Phone
              </label>
              <Input
                defaultValue="+212 5XX XXX XXX"
                className="border-2 border-gray-200 focus:border-italian-green rounded-xl"
              />
            </div>
          </CardContent>
        </Card>

        {/* Database Settings */}
        <Card className="bg-gradient-to-br from-white to-gray-50 border-0 shadow-xl hover:shadow-2xl transition-all duration-500">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center text-xl font-bold text-gray-900 italian-flag-corner">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mr-3 shadow-lg">
                <Database className="h-5 w-5 text-white" />
              </div>
              Database Status
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center justify-between p-4 bg-green-50 rounded-xl">
              <div className="flex items-center space-x-3">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <span className="font-medium text-green-800">Supabase Connection</span>
              </div>
              <Badge variant="success" className="bg-green-100 text-green-800">
                Connected
              </Badge>
            </div>
            <div className="flex items-center justify-between p-4 bg-green-50 rounded-xl">
              <div className="flex items-center space-x-3">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <span className="font-medium text-green-800">Row Level Security</span>
              </div>
              <Badge variant="success" className="bg-green-100 text-green-800">
                Enabled
              </Badge>
            </div>
            <div className="flex items-center justify-between p-4 bg-blue-50 rounded-xl">
              <div className="flex items-center space-x-3">
                <Database className="h-5 w-5 text-blue-600" />
                <span className="font-medium text-blue-800">Database Size</span>
              </div>
              <Badge variant="outline" className="bg-blue-100 text-blue-800">
                ~50MB
              </Badge>
            </div>
            <Button variant="outline" className="w-full border-2 border-gray-200 hover:border-blue-500 hover:bg-blue-500 hover:text-white transition-all duration-300 rounded-xl">
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh Status
            </Button>
          </CardContent>
        </Card>

        {/* Email Settings */}
        <Card className="bg-gradient-to-br from-white to-gray-50 border-0 shadow-xl hover:shadow-2xl transition-all duration-500">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center text-xl font-bold text-gray-900 italian-flag-corner">
              <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mr-3 shadow-lg">
                <Mail className="h-5 w-5 text-white" />
              </div>
              Email Configuration
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                SMTP Server
              </label>
              <Input
                defaultValue="smtp.gmail.com"
                className="border-2 border-gray-200 focus:border-italian-green rounded-xl"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                SMTP Port
              </label>
              <Input
                defaultValue="587"
                className="border-2 border-gray-200 focus:border-italian-green rounded-xl"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                From Email
              </label>
              <Input
                defaultValue="<EMAIL>"
                type="email"
                className="border-2 border-gray-200 focus:border-italian-green rounded-xl"
              />
            </div>
            <div className="flex items-center justify-between p-4 bg-yellow-50 rounded-xl">
              <div className="flex items-center space-x-3">
                <AlertTriangle className="h-5 w-5 text-yellow-600" />
                <span className="font-medium text-yellow-800">Email Service</span>
              </div>
              <Badge variant="warning" className="bg-yellow-100 text-yellow-800">
                Not Configured
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* Security Settings */}
        <Card className="bg-gradient-to-br from-white to-gray-50 border-0 shadow-xl hover:shadow-2xl transition-all duration-500">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center text-xl font-bold text-gray-900 italian-flag-corner">
              <div className="w-10 h-10 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center mr-3 shadow-lg">
                <Shield className="h-5 w-5 text-white" />
              </div>
              Security Settings
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center justify-between p-4 bg-green-50 rounded-xl">
              <div className="flex items-center space-x-3">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <span className="font-medium text-green-800">Two-Factor Authentication</span>
              </div>
              <Badge variant="success" className="bg-green-100 text-green-800">
                Enabled
              </Badge>
            </div>
            <div className="flex items-center justify-between p-4 bg-green-50 rounded-xl">
              <div className="flex items-center space-x-3">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <span className="font-medium text-green-800">SSL Certificate</span>
              </div>
              <Badge variant="success" className="bg-green-100 text-green-800">
                Valid
              </Badge>
            </div>
            <div className="flex items-center justify-between p-4 bg-blue-50 rounded-xl">
              <div className="flex items-center space-x-3">
                <Users className="h-5 w-5 text-blue-600" />
                <span className="font-medium text-blue-800">Active Sessions</span>
              </div>
              <Badge variant="outline" className="bg-blue-100 text-blue-800">
                3 Users
              </Badge>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Session Timeout (minutes)
              </label>
              <Input
                defaultValue="60"
                type="number"
                className="border-2 border-gray-200 focus:border-italian-green rounded-xl"
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Save Button */}
      <Card className="bg-gradient-to-br from-white to-gray-50 border-0 shadow-xl">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {saved && (
                <>
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <span className="text-green-600 font-medium">Settings saved successfully!</span>
                </>
              )}
            </div>
            <Button
              onClick={handleSave}
              disabled={saving}
              className="px-8 py-3 bg-gradient-to-r from-italian-green to-italian-red hover:from-italian-red hover:to-italian-green transition-all duration-300 border-0 rounded-xl shadow-lg hover:shadow-xl font-semibold"
            >
              {saving ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Save Settings
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
