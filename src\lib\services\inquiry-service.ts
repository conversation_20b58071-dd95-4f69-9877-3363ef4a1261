import { supabase } from '@/lib/supabase/client';
import { createClient } from '@supabase/supabase-js';
import type { Database } from '@/types/database';

type PropertyInquiry = Database['public']['Tables']['property_inquiries']['Row'];
type PropertyInquiryInsert = Database['public']['Tables']['property_inquiries']['Insert'];
type PropertyInquiryUpdate = Database['public']['Tables']['property_inquiries']['Update'];

// Create admin client for admin operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export interface InquiryWithProperty extends PropertyInquiry {
  property?: {
    id: string;
    title: string;
    price: number;
    city: string;
    property_type: string;
  };
}

export interface InquirySearchFilters {
  searchQuery?: string;
  inquiryType?: 'purchase' | 'rent' | 'information' | 'all';
  status?: 'new' | 'contacted' | 'scheduled' | 'closed' | 'all';
  propertyId?: string;
  dateFrom?: string;
  dateTo?: string;
}

export interface InquirySearchOptions {
  limit?: number;
  offset?: number;
  sortBy?: keyof PropertyInquiry;
  sortOrder?: 'asc' | 'desc';
}

export class InquiryService {
  static async createInquiry(inquiry: PropertyInquiryInsert): Promise<PropertyInquiry> {
    const { data, error } = await supabase
      .from('property_inquiries')
      .insert(inquiry)
      .select()
      .single();

    if (error) {
      throw error;
    }

    return data;
  }

  static async getInquiries(
    filters: InquirySearchFilters = {},
    options: InquirySearchOptions = {}
  ) {
    const {
      searchQuery,
      inquiryType,
      status,
      propertyId,
      dateFrom,
      dateTo,
    } = filters;

    const {
      limit = 20,
      offset = 0,
      sortBy = 'created_at',
      sortOrder = 'desc',
    } = options;

    let query = supabaseAdmin
      .from('property_inquiries')
      .select(`
        *,
        property:properties(
          id,
          title,
          price,
          city,
          property_type
        )
      `, { count: 'exact' })
      .range(offset, offset + limit - 1)
      .order(sortBy, { ascending: sortOrder === 'asc' });

    // Apply filters
    if (searchQuery) {
      query = query.or(`name.ilike.%${searchQuery}%,email.ilike.%${searchQuery}%,message.ilike.%${searchQuery}%`);
    }

    if (inquiryType && inquiryType !== 'all') {
      query = query.eq('inquiry_type', inquiryType);
    }

    if (status && status !== 'all') {
      query = query.eq('status', status);
    }

    if (propertyId) {
      query = query.eq('property_id', propertyId);
    }

    if (dateFrom) {
      query = query.gte('created_at', dateFrom);
    }

    if (dateTo) {
      query = query.lte('created_at', dateTo);
    }

    const { data, error, count } = await query;

    if (error) {
      throw error;
    }

    return {
      inquiries: data as InquiryWithProperty[] || [],
      total: count || 0,
    };
  }

  static async getInquiryById(id: string): Promise<InquiryWithProperty> {
    const { data, error } = await supabaseAdmin
      .from('property_inquiries')
      .select(`
        *,
        property:properties(
          id,
          title,
          price,
          city,
          property_type,
          images
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      throw error;
    }

    return data as InquiryWithProperty;
  }

  static async updateInquiry(id: string, updates: PropertyInquiryUpdate): Promise<PropertyInquiry> {
    const { data, error } = await supabaseAdmin
      .from('property_inquiries')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw error;
    }

    return data;
  }

  static async updateInquiryStatus(id: string, status: 'new' | 'contacted' | 'scheduled' | 'closed'): Promise<PropertyInquiry> {
    return this.updateInquiry(id, { status });
  }

  static async deleteInquiry(id: string): Promise<void> {
    const { error } = await supabaseAdmin
      .from('property_inquiries')
      .delete()
      .eq('id', id);

    if (error) {
      throw error;
    }
  }

  static async getInquiryStats() {
    const { data: inquiries, error } = await supabaseAdmin
      .from('property_inquiries')
      .select('inquiry_type, status, created_at');

    if (error) {
      throw error;
    }

    const total = inquiries?.length || 0;
    const newInquiries = inquiries?.filter(i => i.status === 'new').length || 0;
    const contacted = inquiries?.filter(i => i.status === 'contacted').length || 0;
    const scheduled = inquiries?.filter(i => i.status === 'scheduled').length || 0;
    const closed = inquiries?.filter(i => i.status === 'closed').length || 0;

    // Calculate inquiries this month
    const thisMonth = new Date();
    thisMonth.setDate(1);
    const thisMonthCount = inquiries?.filter(i => 
      new Date(i.created_at) >= thisMonth
    ).length || 0;

    // Group by inquiry type
    const byInquiryType = inquiries?.reduce((acc, inquiry) => {
      acc[inquiry.inquiry_type] = (acc[inquiry.inquiry_type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>) || {};

    return {
      total,
      newInquiries,
      contacted,
      scheduled,
      closed,
      thisMonthCount,
      byInquiryType,
    };
  }

  static async getInquiriesByProperty(propertyId: string) {
    return this.getInquiries({ propertyId });
  }
}
