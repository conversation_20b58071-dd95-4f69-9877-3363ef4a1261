import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { MessageSquare, Clock, User, ArrowRight, Phone, Mail, Building, Sparkles } from 'lucide-react';

const inquiries = [
  {
    id: '1',
    name: '<PERSON>',
    email: 'ahmed.ben<PERSON>@email.com',
    property: 'Luxury Villa in Casablanca',
    type: 'purchase',
    status: 'new',
    time: '2 hours ago',
    priority: 'high',
    avatar: 'AB'
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    property: 'Modern Apartment in Rabat',
    type: 'rent',
    status: 'contacted',
    time: '5 hours ago',
    priority: 'medium',
    avatar: 'SJ'
  },
  {
    id: '3',
    name: '<PERSON>',
    email: '<EMAIL>',
    property: 'Traditional Riad in Marrakech',
    type: 'information',
    status: 'scheduled',
    time: '1 day ago',
    priority: 'low',
    avatar: 'MA'
  },
];

const getStatusColor = (status: string) => {
  switch (status) {
    case 'new': return 'bg-gradient-to-r from-blue-500 to-blue-600 text-white';
    case 'contacted': return 'bg-gradient-to-r from-italian-green to-green-600 text-white';
    case 'scheduled': return 'bg-gradient-to-r from-purple-500 to-purple-600 text-white';
    default: return 'bg-gradient-to-r from-gray-500 to-gray-600 text-white';
  }
};

const getTypeColor = (type: string) => {
  switch (type) {
    case 'purchase': return 'bg-gradient-to-r from-italian-red to-red-600 text-white';
    case 'rent': return 'bg-gradient-to-r from-orange-500 to-orange-600 text-white';
    case 'information': return 'bg-gradient-to-r from-indigo-500 to-indigo-600 text-white';
    default: return 'bg-gradient-to-r from-gray-500 to-gray-600 text-white';
  }
};

const getPriorityColor = (priority: string) => {
  switch (priority) {
    case 'high': return 'border-red-500 bg-red-50';
    case 'medium': return 'border-yellow-500 bg-yellow-50';
    case 'low': return 'border-green-500 bg-green-50';
    default: return 'border-gray-500 bg-gray-50';
  }
};

export function RecentInquiries() {
  return (
    <Card className="bg-gradient-to-br from-white to-gray-50 border-0 shadow-xl hover:shadow-2xl transition-all duration-500 hover-lift relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-italian-green/10 to-italian-red/10 rounded-full -translate-y-16 translate-x-16"></div>

      <CardHeader className="relative z-10 pb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-gradient-to-br from-italian-green to-italian-red rounded-2xl flex items-center justify-center shadow-lg">
              <MessageSquare className="h-6 w-6 text-white" />
            </div>
            <div>
              <CardTitle className="text-2xl font-bold text-gray-900 italian-flag-corner">
                Recent Inquiries
              </CardTitle>
              <p className="text-gray-600 font-medium">
                Latest customer messages
              </p>
            </div>
          </div>
          <Button variant="outline" className="border-2 border-gray-200 hover:border-italian-green hover:bg-italian-green hover:text-white transition-all duration-300 rounded-xl">
            View All
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </CardHeader>

      <CardContent className="relative z-10 space-y-6">
        {inquiries.map((inquiry, index) => (
          <div
            key={inquiry.id}
            className={`relative overflow-hidden bg-gradient-to-r from-white to-gray-50 rounded-2xl p-6 transition-all duration-500 hover:shadow-xl hover:scale-105 border-l-4 ${getPriorityColor(inquiry.priority)} animate-fade-in group`}
            style={{ animationDelay: `${index * 150}ms` }}
          >
            {/* Background pattern */}
            <div className="absolute inset-0 bg-gradient-to-br from-white/50 to-transparent"></div>
            <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-white/20 to-transparent rounded-full -translate-y-10 translate-x-10"></div>

            <div className="relative z-10">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-4">
                  <div className="w-14 h-14 bg-gradient-to-br from-italian-green to-italian-red rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <span className="text-white font-bold text-lg">{inquiry.avatar}</span>
                  </div>
                  <div>
                    <h3 className="font-bold text-lg text-gray-900 group-hover:text-italian-green transition-colors duration-300">
                      {inquiry.name}
                    </h3>
                    <div className="flex items-center space-x-3 text-sm text-gray-600 mt-1">
                      <div className="flex items-center bg-gray-100 px-2 py-1 rounded-lg">
                        <Mail className="h-3 w-3 mr-1 text-italian-green" />
                        <span className="font-medium">{inquiry.email}</span>
                      </div>
                      <div className="flex items-center bg-gray-100 px-2 py-1 rounded-lg">
                        <Clock className="h-3 w-3 mr-1 text-italian-green" />
                        <span className="font-medium">{inquiry.time}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Button
                    size="sm"
                    variant="outline"
                    className="border-2 border-gray-200 hover:border-italian-green hover:bg-italian-green hover:text-white transition-all duration-300 rounded-xl"
                  >
                    <Phone className="h-4 w-4 mr-1" />
                    Call
                  </Button>
                  <Button
                    size="sm"
                    className="bg-gradient-to-r from-italian-green to-italian-red hover:from-italian-red hover:to-italian-green transition-all duration-300 border-0 rounded-xl shadow-lg"
                  >
                    Respond
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="mb-4">
                <div className="flex items-center space-x-2 mb-2">
                  <Building className="h-4 w-4 text-gray-400" />
                  <span className="font-bold text-gray-900">{inquiry.property}</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Badge className={`${getTypeColor(inquiry.type)} border-0 px-3 py-1 font-semibold`}>
                    {inquiry.type}
                  </Badge>
                  <Badge className={`${getStatusColor(inquiry.status)} border-0 px-3 py-1 font-semibold`}>
                    {inquiry.status}
                  </Badge>
                  <div className={`px-3 py-1 rounded-full text-xs font-bold uppercase tracking-wider ${inquiry.priority === 'high' ? 'text-red-700' : inquiry.priority === 'medium' ? 'text-yellow-700' : 'text-green-700'}`}>
                    {inquiry.priority} Priority
                  </div>
                </div>
              </div>

              {/* Progress indicator */}
              <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
                <div
                  className={`h-full bg-gradient-to-r ${getStatusColor(inquiry.status)} rounded-full transition-all duration-1000 ease-out`}
                  style={{
                    width: inquiry.status === 'new' ? '25%' : inquiry.status === 'contacted' ? '50%' : inquiry.status === 'scheduled' ? '75%' : '100%',
                    animationDelay: `${index * 200 + 500}ms`
                  }}
                ></div>
              </div>
            </div>

            {/* Hover effect overlay */}
            <div className="absolute inset-0 bg-gradient-to-r from-italian-green/5 to-italian-red/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl"></div>
          </div>
        ))}

        {/* Footer */}
        <div className="mt-8 pt-6 border-t border-gray-200">
          <div className="flex items-center justify-center space-x-2 text-gray-500">
            <Sparkles className="h-4 w-4" />
            <span className="text-sm font-medium">
              {inquiries.filter(i => i.status === 'new').length} new inquiries this week
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
