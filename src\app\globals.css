@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Darden PM Color Scheme - Light theme with Italian flag touches */
  --background: #ffffff;
  --foreground: #1a1a1a;
  --card: #ffffff;
  --card-foreground: #1a1a1a;
  --popover: #ffffff;
  --popover-foreground: #1a1a1a;
  --primary: #16a34a; /* Italian green */
  --primary-foreground: #ffffff;
  --secondary: #f5f5f5; /* Light grey */
  --secondary-foreground: #1a1a1a;
  --muted: #f5f5f5;
  --muted-foreground: #6b7280;
  --accent: #f5f5f5;
  --accent-foreground: #1a1a1a;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #e5e7eb;
  --input: #e5e7eb;
  --ring: #16a34a;
  --radius: 0.5rem;

  /* Italian Flag Colors */
  --italian-green: #009246;
  --italian-white: #ffffff;
  --italian-red: #ce2b37;

  /* Italian Flag Gradients */
  --italian-flag-gradient: linear-gradient(90deg, var(--italian-green) 0%, var(--italian-white) 33.33%, var(--italian-white) 66.66%, var(--italian-red) 100%);
  --italian-flag-vertical: linear-gradient(180deg, var(--italian-green) 0%, var(--italian-white) 33.33%, var(--italian-white) 66.66%, var(--italian-red) 100%);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ffffff;
    --card: #1a1a1a;
    --card-foreground: #ffffff;
    --popover: #1a1a1a;
    --popover-foreground: #ffffff;
    --primary: #22c55e;
    --primary-foreground: #000000;
    --secondary: #262626;
    --secondary-foreground: #ffffff;
    --muted: #262626;
    --muted-foreground: #a1a1aa;
    --accent: #262626;
    --accent-foreground: #ffffff;
    --border: #404040;
    --input: #404040;
    --ring: #22c55e;
  }
}

* {
  border-color: hsl(var(--border));
}

body {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary));
}

/* Italian Flag Theme Components */
.italian-flag-corner {
  position: relative;
}

.italian-flag-corner::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 20px;
  height: 20px;
  background: var(--italian-flag-gradient);
  clip-path: polygon(100% 0%, 0% 0%, 100% 100%);
  opacity: 0.8;
}

.italian-flag-underline {
  position: relative;
}

.italian-flag-underline::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--italian-flag-gradient);
  border-radius: 1px;
}

.italian-flag-border {
  border-left: 3px solid var(--italian-green);
  border-right: 3px solid var(--italian-red);
}

.italian-green {
  color: var(--italian-green);
}

.italian-red {
  color: var(--italian-red);
}

.bg-italian-green {
  background-color: var(--italian-green);
}

.bg-italian-red {
  background-color: var(--italian-red);
}

/* Subtle animations */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

.scale-in {
  animation: scaleIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* Hover effects */
.hover-lift {
  transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Modern Hero Animations */
.animate-fade-in {
  animation: fadeIn 1s ease-out;
}

.animate-slide-up {
  animation: slideUp 1s ease-out;
}

.animate-slide-up-delayed {
  animation: slideUp 1s ease-out 0.2s both;
}

.animate-slide-up-delayed-2 {
  animation: slideUp 1s ease-out 0.4s both;
}

.animate-slide-up-delayed-3 {
  animation: slideUp 1s ease-out 0.6s both;
}

.animate-slide-up-delayed-4 {
  animation: slideUp 1s ease-out 0.8s both;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-float-delayed {
  animation: float 6s ease-in-out infinite 2s;
}

.animate-float-slow {
  animation: float 8s ease-in-out infinite;
}

.animate-gradient-x {
  animation: gradientX 3s ease infinite;
  background-size: 200% 200%;
}

.animate-scale-in {
  animation: scaleIn 0.6s ease-out;
}

.animate-fade-in-delay {
  animation: fadeIn 1s ease-out 0.3s both;
}

.animate-bounce-in {
  animation: bounceIn 0.8s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.6s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.6s ease-out;
}

.animate-pulse-slow {
  animation: pulse 3s ease-in-out infinite;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes gradientX {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes glow {
  from {
    box-shadow: 0 0 20px rgba(0, 146, 70, 0.3);
  }
  to {
    box-shadow: 0 0 30px rgba(206, 43, 55, 0.3);
  }
}
