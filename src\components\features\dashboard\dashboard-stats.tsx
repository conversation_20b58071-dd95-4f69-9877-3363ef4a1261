import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Building, MessageSquare, Eye, TrendingUp, ArrowUp, ArrowDown, Sparkles } from 'lucide-react';

const stats = [
  {
    title: 'Total Properties',
    value: '24',
    change: '+2 this month',
    changePercent: '+8.3%',
    icon: Building,
    gradient: 'from-blue-500 to-blue-600',
    bgGradient: 'from-blue-50 to-blue-100',
    trend: 'up',
    description: 'Active listings'
  },
  {
    title: 'Active Inquiries',
    value: '12',
    change: '+3 this week',
    changePercent: '+25%',
    icon: MessageSquare,
    gradient: 'from-italian-green to-green-600',
    bgGradient: 'from-green-50 to-green-100',
    trend: 'up',
    description: 'Pending responses'
  },
  {
    title: 'Property Views',
    value: '1,234',
    change: '+15% this month',
    changePercent: '+15%',
    icon: Eye,
    gradient: 'from-purple-500 to-purple-600',
    bgGradient: 'from-purple-50 to-purple-100',
    trend: 'up',
    description: 'Total impressions'
  },
  {
    title: 'Revenue',
    value: '125,000 MAD',
    change: '+8% this month',
    changePercent: '+8%',
    icon: TrendingUp,
    gradient: 'from-italian-red to-red-600',
    bgGradient: 'from-red-50 to-red-100',
    trend: 'up',
    description: 'Monthly earnings'
  },
];

export function DashboardStats() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
      {stats.map((stat, index) => {
        const Icon = stat.icon;
        const TrendIcon = stat.trend === 'up' ? ArrowUp : ArrowDown;

        return (
          <Card
            key={index}
            className="relative overflow-hidden bg-gradient-to-br from-white to-gray-50 border-0 shadow-xl hover:shadow-2xl transition-all duration-500 hover-lift group animate-scale-in"
            style={{ animationDelay: `${index * 150}ms` }}
          >
            {/* Background Pattern */}
            <div className={`absolute inset-0 bg-gradient-to-br ${stat.bgGradient} opacity-30`}></div>
            <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-white/20 to-transparent rounded-full -translate-y-16 translate-x-16"></div>

            <CardHeader className="relative z-10 pb-4">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <CardTitle className="text-sm font-bold text-gray-600 mb-2 group-hover:text-gray-800 transition-colors duration-300">
                    {stat.title}
                  </CardTitle>
                  <p className="text-xs text-gray-500 font-medium">
                    {stat.description}
                  </p>
                </div>
                <div className={`w-16 h-16 bg-gradient-to-br ${stat.gradient} rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 group-hover:shadow-xl transition-all duration-300`}>
                  <Icon className="h-8 w-8 text-white" />
                </div>
              </div>
            </CardHeader>

            <CardContent className="relative z-10 pt-0">
              <div className="space-y-4">
                <div className="text-4xl font-bold text-gray-900 group-hover:text-italian-green transition-colors duration-300">
                  {stat.value}
                </div>

                <div className="flex items-center justify-between">
                  <div className={`flex items-center px-3 py-1 rounded-full ${stat.trend === 'up' ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`}>
                    <TrendIcon className="h-4 w-4 mr-1" />
                    <span className="text-sm font-bold">{stat.changePercent}</span>
                  </div>
                  <div className="text-xs text-gray-500 font-medium">
                    {stat.change}
                  </div>
                </div>

                {/* Progress bar */}
                <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
                  <div
                    className={`h-full bg-gradient-to-r ${stat.gradient} rounded-full transition-all duration-1000 ease-out`}
                    style={{
                      width: `${Math.min(parseInt(stat.changePercent.replace('%', '').replace('+', '')) * 3, 100)}%`,
                      animationDelay: `${index * 200 + 500}ms`
                    }}
                  ></div>
                </div>
              </div>
            </CardContent>

            {/* Sparkle decoration */}
            <div className="absolute bottom-4 right-4 opacity-20 group-hover:opacity-40 transition-opacity duration-300">
              <Sparkles className="h-6 w-6 text-gray-400" />
            </div>
          </Card>
        );
      })}
    </div>
  );
}
