'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils/cn';
import { useAuth } from '@/hooks/use-auth';
import { APP_CONFIG } from '@/constants/app';
import {
  Home,
  Building,
  Users,
  MessageSquare,
  FileText,
  Settings,
  BarChart3,
  Menu,
  X,
  ChevronRight,
  Sparkles,
  TrendingUp,
  Shield,
  Zap,
  Mail
} from 'lucide-react';

const navigation = [
  {
    name: 'Dashboard',
    href: '/dashboard',
    icon: Home,
    description: 'Overview & Analytics',
    gradient: 'from-blue-500 to-blue-600'
  },
  {
    name: 'Properties',
    href: '/dashboard/properties',
    icon: Building,
    description: 'Manage Properties',
    gradient: 'from-italian-green to-green-600'
  },
  {
    name: 'Inquiries',
    href: '/dashboard/inquiries',
    icon: MessageSquare,
    description: 'Customer Messages',
    gradient: 'from-purple-500 to-purple-600'
  },
  {
    name: 'Analytics',
    href: '/dashboard/analytics',
    icon: BarChart3,
    description: 'Performance Metrics',
    gradient: 'from-orange-500 to-orange-600'
  },
];

const adminNavigation = [
  {
    name: 'Users',
    href: '/admin/users',
    icon: Users,
    description: 'User Management',
    gradient: 'from-indigo-500 to-indigo-600'
  },
  {
    name: 'Properties',
    href: '/admin/properties',
    icon: Building,
    description: 'Property Management',
    gradient: 'from-green-500 to-green-600'
  },
  {
    name: 'Inquiries',
    href: '/admin/inquiries',
    icon: MessageSquare,
    description: 'Property Inquiries',
    gradient: 'from-purple-500 to-purple-600'
  },
  {
    name: 'Contacts',
    href: '/admin/contacts',
    icon: Mail,
    description: 'Contact Management',
    gradient: 'from-blue-500 to-blue-600'
  },
  {
    name: 'Blog Posts',
    href: '/admin/blog',
    icon: FileText,
    description: 'Content Management',
    gradient: 'from-pink-500 to-pink-600'
  },
  {
    name: 'Settings',
    href: '/admin/settings',
    icon: Settings,
    description: 'System Configuration',
    gradient: 'from-gray-500 to-gray-600'
  },
];

export function DashboardSidebar() {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const pathname = usePathname();
  const { isAdmin } = useAuth();

  const allNavigation = isAdmin ? [...navigation, ...adminNavigation] : navigation;

  return (
    <>
      {/* Mobile sidebar */}
      <div className={cn(
        'fixed inset-0 z-50 lg:hidden',
        sidebarOpen ? 'block' : 'hidden'
      )}>
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm" onClick={() => setSidebarOpen(false)} />
        <div className="fixed inset-y-0 left-0 flex w-80 flex-col bg-gradient-to-br from-white via-gray-50 to-white shadow-2xl">
          {/* Mobile Header */}
          <div className="relative bg-gradient-to-r from-italian-green via-white to-italian-red p-1">
            <div className="bg-white rounded-lg mx-1 my-1">
              <div className="flex h-20 items-center justify-between px-6">
                <Link href="/" className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-gradient-to-br from-italian-green to-italian-red rounded-2xl flex items-center justify-center shadow-lg">
                    <span className="text-white font-bold text-2xl">D</span>
                  </div>
                  <div className="flex flex-col">
                    <span className="font-bold text-xl text-gray-900">
                      Darden PM
                    </span>
                    <span className="text-xs text-gray-500 font-medium">
                      Property Management
                    </span>
                  </div>
                </Link>
                <button
                  onClick={() => setSidebarOpen(false)}
                  className="w-10 h-10 rounded-xl bg-gray-100 hover:bg-gray-200 flex items-center justify-center text-gray-500 hover:text-gray-700 transition-all duration-300"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>
            </div>
          </div>

          {/* Mobile Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-3 overflow-y-auto">
            {allNavigation.map((item, index) => {
              const Icon = item.icon;
              const isActive = pathname === item.href;
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={cn(
                    'group flex items-center px-4 py-4 rounded-2xl transition-all duration-300 animate-fade-in',
                    isActive
                      ? 'bg-gradient-to-r from-italian-green to-italian-red text-white shadow-lg transform scale-105'
                      : 'text-gray-700 hover:bg-gradient-to-r hover:from-gray-100 hover:to-gray-50 hover:shadow-md hover:scale-105'
                  )}
                  style={{ animationDelay: `${index * 50}ms` }}
                  onClick={() => setSidebarOpen(false)}
                >
                  <div className={cn(
                    'w-12 h-12 rounded-xl flex items-center justify-center mr-4 transition-all duration-300',
                    isActive
                      ? 'bg-white/20 shadow-lg'
                      : `bg-gradient-to-br ${item.gradient} text-white shadow-md group-hover:scale-110`
                  )}>
                    <Icon className="h-6 w-6" />
                  </div>
                  <div className="flex-1">
                    <div className="font-bold text-lg">{item.name}</div>
                    <div className={cn(
                      'text-sm opacity-75',
                      isActive ? 'text-white/80' : 'text-gray-500'
                    )}>
                      {item.description}
                    </div>
                  </div>
                  <ChevronRight className={cn(
                    'h-5 w-5 transition-transform duration-300',
                    isActive ? 'text-white/80' : 'text-gray-400 group-hover:translate-x-1'
                  )} />
                </Link>
              );
            })}
          </nav>

          {/* Mobile Footer */}
          <div className="p-4 border-t border-gray-200">
            <div className="bg-gradient-to-r from-italian-green/10 to-italian-red/10 rounded-2xl p-4 text-center">
              <Sparkles className="h-8 w-8 text-italian-green mx-auto mb-2" />
              <p className="text-sm font-medium text-gray-700">
                Premium Dashboard
              </p>
              <p className="text-xs text-gray-500">
                Manage your properties with style
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-80 lg:flex-col">
        <div className="flex flex-col flex-grow bg-gradient-to-br from-white via-gray-50 to-white shadow-2xl border-r-4 border-gradient-to-b border-italian-green/20">
          {/* Desktop Header */}
          <div className="relative bg-gradient-to-r from-italian-green via-white to-italian-red p-1">
            <div className="bg-white rounded-2xl mx-2 my-2">
              <div className="flex h-24 items-center px-6">
                <Link href="/" className="flex items-center space-x-4 group">
                  <div className="w-16 h-16 bg-gradient-to-br from-italian-green to-italian-red rounded-3xl flex items-center justify-center shadow-xl group-hover:scale-110 transition-transform duration-300">
                    <span className="text-white font-bold text-3xl">D</span>
                  </div>
                  <div className="flex flex-col">
                    <span className="font-bold text-2xl text-gray-900 group-hover:text-italian-green transition-colors duration-300">
                      Darden PM
                    </span>
                    <span className="text-sm text-gray-500 font-medium">
                      Property Management
                    </span>
                  </div>
                </Link>
              </div>
            </div>
          </div>

          {/* Desktop Navigation */}
          <nav className="flex-1 px-4 py-8 space-y-4 overflow-y-auto">
            {allNavigation.map((item, index) => {
              const Icon = item.icon;
              const isActive = pathname === item.href;
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={cn(
                    'group flex items-center px-6 py-5 rounded-2xl transition-all duration-500 relative overflow-hidden animate-fade-in',
                    isActive
                      ? 'bg-gradient-to-r from-italian-green to-italian-red text-white shadow-2xl transform scale-105'
                      : 'text-gray-700 hover:bg-gradient-to-r hover:from-gray-100 hover:to-gray-50 hover:shadow-xl hover:scale-105'
                  )}
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  {/* Background decoration for active item */}
                  {isActive && (
                    <div className="absolute inset-0 bg-gradient-to-r from-italian-green/20 to-italian-red/20 rounded-2xl blur-xl"></div>
                  )}

                  <div className={cn(
                    'w-14 h-14 rounded-2xl flex items-center justify-center mr-5 transition-all duration-300 relative z-10',
                    isActive
                      ? 'bg-white/20 shadow-2xl backdrop-blur-sm'
                      : `bg-gradient-to-br ${item.gradient} text-white shadow-lg group-hover:scale-110 group-hover:shadow-xl`
                  )}>
                    <Icon className="h-7 w-7" />
                  </div>

                  <div className="flex-1 relative z-10">
                    <div className="font-bold text-lg mb-1">{item.name}</div>
                    <div className={cn(
                      'text-sm opacity-75 font-medium',
                      isActive ? 'text-white/80' : 'text-gray-500 group-hover:text-gray-600'
                    )}>
                      {item.description}
                    </div>
                  </div>

                  <div className="relative z-10">
                    <ChevronRight className={cn(
                      'h-6 w-6 transition-all duration-300',
                      isActive
                        ? 'text-white/80 transform translate-x-1'
                        : 'text-gray-400 group-hover:text-italian-green group-hover:translate-x-2'
                    )} />
                  </div>

                  {/* Active indicator */}
                  {isActive && (
                    <div className="absolute right-0 top-1/2 transform -translate-y-1/2 w-2 h-12 bg-white rounded-l-full shadow-lg"></div>
                  )}
                </Link>
              );
            })}
          </nav>

          {/* Desktop Footer */}
          <div className="p-6 border-t border-gray-200">
            <div className="bg-gradient-to-br from-italian-green/10 via-white to-italian-red/10 rounded-3xl p-6 text-center relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-r from-italian-green/5 to-italian-red/5 rounded-3xl"></div>
              <div className="relative z-10">
                <div className="w-12 h-12 bg-gradient-to-br from-italian-green to-italian-red rounded-2xl flex items-center justify-center mx-auto mb-3 shadow-lg">
                  <Sparkles className="h-6 w-6 text-white" />
                </div>
                <p className="font-bold text-gray-800 mb-1">
                  Premium Dashboard
                </p>
                <p className="text-sm text-gray-600">
                  Manage properties with style
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile menu button */}
      <div className="lg:hidden">
        <button
          onClick={() => setSidebarOpen(true)}
          className="fixed top-6 left-6 z-40 w-14 h-14 rounded-2xl bg-gradient-to-br from-italian-green to-italian-red shadow-2xl text-white hover:shadow-3xl hover:scale-110 transition-all duration-300 flex items-center justify-center"
        >
          <Menu className="h-7 w-7" />
        </button>
      </div>
    </>
  );
}
