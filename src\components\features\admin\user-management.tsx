'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Search,
  Filter,
  MoreHorizontal,
  Edit,
  UserPlus,
  Mail,
  Phone,
  Calendar,
  Loader2
} from 'lucide-react';
import { UserService } from '@/lib/services/user-service';
import type { Database } from '@/types/database';

type User = Database['public']['Tables']['users']['Row'];

export function UserManagement() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedRole, setSelectedRole] = useState<'all' | 'admin' | 'sales_person' | 'user'>('all');
  const [total, setTotal] = useState(0);

  // Load users from Supabase
  useEffect(() => {
    loadUsers();
  }, [searchQuery, selectedRole]);

  const loadUsers = async () => {
    try {
      setLoading(true);
      const { users: fetchedUsers, total: totalCount } = await UserService.getUsers(
        {
          searchQuery: searchQuery || undefined,
          role: selectedRole,
        },
        {
          limit: 50, // Load more users for admin
          offset: 0,
        }
      );
      setUsers(fetchedUsers);
      setTotal(totalCount);
    } catch (error) {
      console.error('Error loading users:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredUsers = users;

  const getRoleColor = (role: string): 'destructive' | 'warning' | 'secondary' => {
    switch (role) {
      case 'admin': return 'destructive';
      case 'sales_person': return 'warning';
      case 'user': return 'secondary';
      default: return 'secondary';
    }
  };

  const getStatusColor = (user: User): 'success' | 'secondary' => {
    // For now, we'll consider all users active since we don't have a status field
    // You can add logic here based on last login or other criteria
    return 'success';
  };

  return (
    <div className="space-y-8 min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100">
      {/* Header Actions */}
      <div className="bg-gradient-to-r from-white to-gray-50 rounded-2xl p-6 shadow-lg border-0">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
          <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-4 sm:space-y-0 sm:space-x-6">
            <div className="relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-italian-green h-5 w-5" />
              <Input
                placeholder="Search users..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-12 w-80 h-12 border-2 border-gray-200 focus:border-italian-green rounded-xl bg-white shadow-sm"
              />
            </div>

            <select
              value={selectedRole}
              onChange={(e) => setSelectedRole(e.target.value)}
              className="border-2 border-gray-200 focus:border-italian-green rounded-xl px-4 py-3 h-12 bg-white shadow-sm font-medium text-gray-700"
            >
              <option value="all">All Roles</option>
              <option value="admin">Admin</option>
              <option value="sales_person">Sales Person</option>
              <option value="user">User</option>
            </select>

            <Button variant="outline" className="h-12 px-6 border-2 border-gray-200 hover:border-italian-green hover:bg-italian-green hover:text-white transition-all duration-300 rounded-xl">
              <Filter className="mr-2 h-5 w-5" />
              More Filters
            </Button>
          </div>

          <Button className="h-12 px-8 bg-gradient-to-r from-italian-green to-italian-red hover:from-italian-red hover:to-italian-green transition-all duration-300 border-0 rounded-xl shadow-lg hover:shadow-xl font-semibold">
            <UserPlus className="mr-3 h-5 w-5" />
            Add User
          </Button>
        </div>
      </div>

      {/* Users Table */}
      <Card className="bg-gradient-to-br from-white to-gray-50 border-0 shadow-xl hover:shadow-2xl transition-all duration-500">
        <CardHeader className="pb-4">
          <CardTitle className="text-2xl font-bold text-gray-900 italian-flag-corner">
            Users ({total})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-italian-green" />
              <span className="ml-2 text-gray-600">Loading users...</span>
            </div>
          ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b-2 border-gray-200">
                  <th className="text-left py-4 px-6 font-bold text-gray-900 bg-gradient-to-r from-gray-50 to-white">User</th>
                  <th className="text-left py-4 px-6 font-bold text-gray-900 bg-gradient-to-r from-gray-50 to-white">Role</th>
                  <th className="text-left py-4 px-6 font-bold text-gray-900 bg-gradient-to-r from-gray-50 to-white">Status</th>
                  <th className="text-left py-4 px-6 font-bold text-gray-900 bg-gradient-to-r from-gray-50 to-white">Last Login</th>
                  <th className="text-left py-4 px-6 font-bold text-gray-900 bg-gradient-to-r from-gray-50 to-white">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredUsers.map((user, index) => (
                  <tr key={user.id} className="border-b border-gray-100 hover:bg-gradient-to-r hover:from-italian-green/5 hover:to-italian-red/5 transition-all duration-300 animate-fade-in" style={{ animationDelay: `${index * 50}ms` }}>
                    <td className="py-6 px-6">
                      <div className="flex items-center space-x-4">
                        <div className="w-12 h-12 bg-gradient-to-br from-italian-green to-italian-red rounded-full flex items-center justify-center shadow-lg">
                          <span className="text-sm font-bold text-white">
                            {user.full_name.split(' ').map(n => n[0]).join('')}
                          </span>
                        </div>
                        <div>
                          <div className="font-bold text-gray-900 text-lg">{user.full_name || 'No Name'}</div>
                          <div className="flex items-center text-sm text-gray-600 mt-1">
                            <Mail className="h-4 w-4 mr-2 text-italian-green" />
                            {user.email}
                          </div>
                          {user.phone && (
                            <div className="flex items-center text-sm text-gray-600 mt-1">
                              <Phone className="h-4 w-4 mr-2 text-italian-green" />
                              {user.phone}
                            </div>
                          )}
                        </div>
                      </div>
                    </td>
                    <td className="py-6 px-6">
                      <Badge variant={getRoleColor(user.role)} className="px-3 py-1 text-sm font-semibold">
                        {user.role.replace('_', ' ')}
                      </Badge>
                    </td>
                    <td className="py-6 px-6">
                      <Badge variant={getStatusColor(user)} className="px-3 py-1 text-sm font-semibold">
                        Active
                      </Badge>
                    </td>
                    <td className="py-6 px-6">
                      <div className="flex items-center text-sm text-gray-600 bg-gray-50 px-3 py-2 rounded-lg">
                        <Calendar className="h-4 w-4 mr-2 text-italian-green" />
                        {new Date(user.created_at).toLocaleDateString()}
                      </div>
                    </td>
                    <td className="py-6 px-6">
                      <div className="flex items-center space-x-3">
                        <Button variant="outline" size="sm" className="hover:bg-italian-green hover:text-white transition-all duration-300 border-2">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm" className="hover:bg-italian-red hover:text-white transition-all duration-300 border-2">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          )}
        </CardContent>
      </Card>

      {/* Pagination */}
      <div className="flex justify-center">
        <div className="flex items-center space-x-3 bg-gradient-to-r from-white to-gray-50 p-4 rounded-2xl shadow-lg border-0">
          <Button variant="outline" disabled className="border-2 border-gray-200 rounded-xl px-6 py-2">
            Previous
          </Button>
          <Button variant="outline" className="border-2 border-gray-200 hover:border-italian-green hover:bg-italian-green hover:text-white transition-all duration-300 rounded-xl px-4 py-2">
            1
          </Button>
          <Button className="bg-gradient-to-r from-italian-green to-italian-red border-0 rounded-xl px-4 py-2 shadow-lg">
            2
          </Button>
          <Button variant="outline" className="border-2 border-gray-200 hover:border-italian-green hover:bg-italian-green hover:text-white transition-all duration-300 rounded-xl px-4 py-2">
            3
          </Button>
          <Button variant="outline" className="border-2 border-gray-200 hover:border-italian-green hover:bg-italian-green hover:text-white transition-all duration-300 rounded-xl px-6 py-2">
            Next
          </Button>
        </div>
      </div>
    </div>
  );
}
