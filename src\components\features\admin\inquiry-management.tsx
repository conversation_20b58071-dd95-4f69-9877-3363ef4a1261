'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Search,
  Filter,
  Mail,
  Phone,
  Calendar,
  User,
  Building,
  MessageSquare,
  CheckCircle,
  Clock,
  AlertCircle,
  Loader2,
  Eye,
  Edit
} from 'lucide-react';
import { InquiryService, type InquiryWithProperty } from '@/lib/services/inquiry-service';

export function InquiryManagement() {
  const [inquiries, setInquiries] = useState<InquiryWithProperty[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<'all' | 'new' | 'contacted' | 'scheduled' | 'closed'>('all');
  const [selectedType, setSelectedType] = useState<'all' | 'purchase' | 'rent' | 'information'>('all');
  const [total, setTotal] = useState(0);

  // Load inquiries from Supabase
  useEffect(() => {
    loadInquiries();
  }, [searchQuery, selectedStatus, selectedType]);

  const loadInquiries = async () => {
    try {
      setLoading(true);
      const { inquiries: fetchedInquiries, total: totalCount } = await InquiryService.getInquiries(
        {
          searchQuery: searchQuery || undefined,
          status: selectedStatus === 'all' ? undefined : selectedStatus,
          inquiryType: selectedType === 'all' ? undefined : selectedType,
        },
        {
          limit: 50,
          offset: 0,
        }
      );
      setInquiries(fetchedInquiries);
      setTotal(totalCount);
    } catch (error) {
      console.error('Error loading inquiries:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string): 'success' | 'warning' | 'destructive' | 'secondary' => {
    switch (status) {
      case 'new': return 'destructive';
      case 'contacted': return 'warning';
      case 'scheduled': return 'success';
      case 'closed': return 'secondary';
      default: return 'secondary';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'new': return AlertCircle;
      case 'contacted': return MessageSquare;
      case 'scheduled': return Calendar;
      case 'closed': return CheckCircle;
      default: return Clock;
    }
  };

  const updateInquiryStatus = async (id: string, status: 'new' | 'contacted' | 'scheduled' | 'closed') => {
    try {
      await InquiryService.updateInquiryStatus(id, status);
      loadInquiries(); // Reload the list
    } catch (error) {
      console.error('Error updating inquiry status:', error);
    }
  };

  return (
    <div className="space-y-8 min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100">
      {/* Header Actions */}
      <div className="bg-gradient-to-r from-white to-gray-50 rounded-2xl p-6 shadow-lg border-0">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
          <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-4 sm:space-y-0 sm:space-x-6">
            <div className="relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-italian-green h-5 w-5" />
              <Input
                placeholder="Search inquiries..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-12 w-80 h-12 border-2 border-gray-200 focus:border-italian-green rounded-xl bg-white shadow-sm"
              />
            </div>

            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value as any)}
              className="border-2 border-gray-200 focus:border-italian-green rounded-xl px-4 py-3 h-12 bg-white shadow-sm font-medium text-gray-700"
            >
              <option value="all">All Status</option>
              <option value="new">New</option>
              <option value="contacted">Contacted</option>
              <option value="scheduled">Scheduled</option>
              <option value="closed">Closed</option>
            </select>

            <select
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value as any)}
              className="border-2 border-gray-200 focus:border-italian-green rounded-xl px-4 py-3 h-12 bg-white shadow-sm font-medium text-gray-700"
            >
              <option value="all">All Types</option>
              <option value="purchase">Purchase</option>
              <option value="rent">Rent</option>
              <option value="information">Information</option>
            </select>

            <Button variant="outline" className="h-12 px-6 border-2 border-gray-200 hover:border-italian-green hover:bg-italian-green hover:text-white transition-all duration-300 rounded-xl">
              <Filter className="mr-2 h-5 w-5" />
              More Filters
            </Button>
          </div>
        </div>
      </div>

      {/* Inquiries List */}
      <Card className="bg-gradient-to-br from-white to-gray-50 border-0 shadow-xl hover:shadow-2xl transition-all duration-500">
        <CardHeader className="pb-4">
          <CardTitle className="text-2xl font-bold text-gray-900 italian-flag-corner">
            Property Inquiries ({total})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-italian-green" />
              <span className="ml-2 text-gray-600">Loading inquiries...</span>
            </div>
          ) : (
            <div className="space-y-4">
              {inquiries.map((inquiry, index) => {
                const StatusIcon = getStatusIcon(inquiry.status);
                return (
                  <Card key={inquiry.id} className="hover:shadow-md transition-all duration-300 animate-fade-in" style={{ animationDelay: `${index * 50}ms` }}>
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-3">
                            <div className="w-10 h-10 bg-gradient-to-br from-italian-green to-italian-red rounded-full flex items-center justify-center shadow-lg">
                              <User className="h-5 w-5 text-white" />
                            </div>
                            <div>
                              <h3 className="font-semibold text-gray-900 text-lg">{inquiry.name}</h3>
                              <div className="flex items-center space-x-4 text-sm text-gray-600">
                                <div className="flex items-center">
                                  <Mail className="h-3 w-3 mr-1 text-italian-green" />
                                  {inquiry.email}
                                </div>
                                {inquiry.phone && (
                                  <div className="flex items-center">
                                    <Phone className="h-3 w-3 mr-1 text-italian-green" />
                                    {inquiry.phone}
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>

                          {inquiry.property && (
                            <div className="bg-gray-50 rounded-lg p-3 mb-3">
                              <div className="flex items-center space-x-2 text-sm">
                                <Building className="h-4 w-4 text-italian-green" />
                                <span className="font-medium">{inquiry.property.title}</span>
                                <Badge variant="outline" className="text-xs">
                                  {inquiry.property.property_type}
                                </Badge>
                                <span className="text-gray-600">
                                  {new Intl.NumberFormat('en-US').format(inquiry.property.price)} MAD
                                </span>
                              </div>
                            </div>
                          )}

                          {inquiry.message && (
                            <div className="bg-blue-50 rounded-lg p-3 mb-3">
                              <p className="text-sm text-gray-700 italic">"{inquiry.message}"</p>
                            </div>
                          )}

                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-4">
                              <Badge variant={getStatusColor(inquiry.status)} className="px-3 py-1 text-sm font-semibold">
                                <StatusIcon className="h-3 w-3 mr-1" />
                                {inquiry.status}
                              </Badge>
                              <Badge variant="outline" className="px-3 py-1 text-sm">
                                {inquiry.inquiry_type}
                              </Badge>
                              <div className="flex items-center text-sm text-gray-500">
                                <Calendar className="h-3 w-3 mr-1" />
                                {new Date(inquiry.created_at).toLocaleDateString()}
                              </div>
                            </div>

                            <div className="flex items-center space-x-2">
                              <select
                                value={inquiry.status}
                                onChange={(e) => updateInquiryStatus(inquiry.id, e.target.value as any)}
                                className="text-sm border border-gray-300 rounded px-2 py-1 focus:border-italian-green"
                              >
                                <option value="new">New</option>
                                <option value="contacted">Contacted</option>
                                <option value="scheduled">Scheduled</option>
                                <option value="closed">Closed</option>
                              </select>
                              <Button variant="outline" size="sm" className="hover:bg-italian-green hover:text-white transition-all duration-300">
                                <Eye className="h-4 w-4" />
                              </Button>
                              <Button variant="outline" size="sm" className="hover:bg-blue-500 hover:text-white transition-all duration-300">
                                <Edit className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
