'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/hooks/use-auth';
import {
  Bell,
  Search,
  User,
  LogOut,
  Settings,
  MessageSquare,
  Calendar,
  ChevronDown,
  Sparkles,
  Crown,
  Shield
} from 'lucide-react';

export function DashboardHeader() {
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);
  const { user, signOut } = useAuth();

  const notifications = [
    { id: 1, title: 'New property inquiry', message: 'Someone is interested in Villa Casablanca', time: '2 min ago', unread: true },
    { id: 2, title: 'Property listing approved', message: 'Your Marrakech apartment is now live', time: '1 hour ago', unread: true },
    { id: 3, title: 'Monthly report ready', message: 'Your analytics report is available', time: '3 hours ago', unread: false },
  ];

  const unreadCount = notifications.filter(n => n.unread).length;

  return (
    <header className="bg-gradient-to-r from-white via-gray-50 to-white shadow-xl border-b-4 border-gradient-to-r border-italian-green/20">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="flex h-20 items-center justify-between">
          {/* Left Section - Search */}
          <div className="flex items-center flex-1 max-w-2xl">
            <div className="flex-shrink-0 lg:hidden mr-4">
              {/* Mobile menu button space */}
            </div>
            <div className="hidden md:block flex-1">
              <div className="relative max-w-lg">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-italian-green h-5 w-5" />
                <input
                  type="text"
                  placeholder="Search properties, users, inquiries..."
                  className="w-full pl-12 pr-6 py-4 bg-white border-2 border-gray-200 rounded-2xl focus:outline-none focus:ring-4 focus:ring-italian-green/20 focus:border-italian-green transition-all duration-300 shadow-lg text-gray-700 placeholder-gray-500"
                />
                <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                  <kbd className="px-2 py-1 text-xs font-semibold text-gray-500 bg-gray-100 border border-gray-200 rounded">
                    ⌘K
                  </kbd>
                </div>
              </div>
            </div>
          </div>

          {/* Right Section - Actions & User */}
          <div className="flex items-center space-x-4">
            {/* Quick Actions */}
            <div className="hidden lg:flex items-center space-x-3">
              <Button
                variant="outline"
                className="border-2 border-gray-200 hover:border-italian-green hover:bg-italian-green hover:text-white transition-all duration-300 rounded-xl px-4 py-2"
              >
                <MessageSquare className="mr-2 h-4 w-4" />
                Messages
              </Button>
              <Button
                variant="outline"
                className="border-2 border-gray-200 hover:border-italian-red hover:bg-italian-red hover:text-white transition-all duration-300 rounded-xl px-4 py-2"
              >
                <Calendar className="mr-2 h-4 w-4" />
                Schedule
              </Button>
            </div>

            {/* Notifications */}
            <div className="relative">
              <Button
                variant="ghost"
                size="icon"
                className="relative w-12 h-12 rounded-2xl hover:bg-gradient-to-br hover:from-italian-green/10 hover:to-italian-red/10 transition-all duration-300"
                onClick={() => setShowNotifications(!showNotifications)}
              >
                <Bell className="h-6 w-6 text-gray-600" />
                {unreadCount > 0 && (
                  <span className="absolute -top-1 -right-1 h-6 w-6 bg-gradient-to-r from-italian-red to-red-600 text-white rounded-full text-xs font-bold flex items-center justify-center shadow-lg animate-pulse">
                    {unreadCount}
                  </span>
                )}
              </Button>

              {showNotifications && (
                <div className="absolute right-0 mt-4 w-96 bg-white rounded-3xl shadow-2xl border-0 z-50 overflow-hidden">
                  <div className="bg-gradient-to-r from-italian-green to-italian-red p-6 text-white">
                    <h3 className="font-bold text-lg">Notifications</h3>
                    <p className="text-white/80 text-sm">You have {unreadCount} unread notifications</p>
                  </div>

                  <div className="max-h-96 overflow-y-auto">
                    {notifications.map((notification) => (
                      <div
                        key={notification.id}
                        className={`p-4 border-b border-gray-100 hover:bg-gray-50 transition-colors duration-200 ${notification.unread ? 'bg-blue-50/50' : ''}`}
                      >
                        <div className="flex items-start space-x-3">
                          <div className={`w-3 h-3 rounded-full mt-2 ${notification.unread ? 'bg-italian-green' : 'bg-gray-300'}`}></div>
                          <div className="flex-1">
                            <h4 className="font-semibold text-gray-900 text-sm">{notification.title}</h4>
                            <p className="text-gray-600 text-sm mt-1">{notification.message}</p>
                            <p className="text-gray-400 text-xs mt-2">{notification.time}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="p-4 bg-gray-50 text-center">
                    <Button variant="ghost" className="text-italian-green hover:text-italian-red font-medium">
                      View all notifications
                    </Button>
                  </div>
                </div>
              )}
            </div>

            {/* User menu */}
            <div className="relative">
              <Button
                variant="ghost"
                onClick={() => setShowUserMenu(!showUserMenu)}
                className="flex items-center space-x-3 px-4 py-3 rounded-2xl hover:bg-gradient-to-br hover:from-italian-green/10 hover:to-italian-red/10 transition-all duration-300"
              >
                <div className="w-12 h-12 bg-gradient-to-br from-italian-green to-italian-red rounded-2xl flex items-center justify-center shadow-lg">
                  <User className="h-6 w-6 text-white" />
                </div>
                <div className="hidden md:block text-left">
                  <div className="text-sm font-bold text-gray-900">
                    {user?.full_name || 'User'}
                  </div>
                  <div className="text-xs text-gray-500 flex items-center">
                    <Crown className="h-3 w-3 mr-1 text-italian-green" />
                    Premium User
                  </div>
                </div>
                <ChevronDown className="h-4 w-4 text-gray-500" />
              </Button>

              {showUserMenu && (
                <div className="absolute right-0 mt-4 w-72 bg-white rounded-3xl shadow-2xl border-0 z-50 overflow-hidden">
                  {/* User Info Header */}
                  <div className="bg-gradient-to-r from-italian-green to-italian-red p-6 text-white">
                    <div className="flex items-center space-x-4">
                      <div className="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center">
                        <User className="h-8 w-8 text-white" />
                      </div>
                      <div>
                        <div className="font-bold text-lg">{user?.full_name || 'User'}</div>
                        <div className="text-white/80 text-sm">{user?.email}</div>
                        <div className="flex items-center mt-1">
                          <Shield className="h-4 w-4 mr-1" />
                          <span className="text-xs font-medium">Premium Account</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Menu Items */}
                  <div className="p-2">
                    <button
                      className="flex items-center w-full px-4 py-3 text-sm text-gray-700 hover:bg-gradient-to-r hover:from-gray-100 hover:to-gray-50 rounded-2xl transition-all duration-300 group"
                      onClick={() => setShowUserMenu(false)}
                    >
                      <div className="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center mr-3 group-hover:scale-110 transition-transform duration-300">
                        <User className="h-5 w-5 text-blue-600" />
                      </div>
                      <div className="text-left">
                        <div className="font-medium">Profile Settings</div>
                        <div className="text-xs text-gray-500">Manage your account</div>
                      </div>
                    </button>

                    <button
                      className="flex items-center w-full px-4 py-3 text-sm text-gray-700 hover:bg-gradient-to-r hover:from-gray-100 hover:to-gray-50 rounded-2xl transition-all duration-300 group"
                      onClick={() => setShowUserMenu(false)}
                    >
                      <div className="w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center mr-3 group-hover:scale-110 transition-transform duration-300">
                        <Settings className="h-5 w-5 text-green-600" />
                      </div>
                      <div className="text-left">
                        <div className="font-medium">Preferences</div>
                        <div className="text-xs text-gray-500">Customize your experience</div>
                      </div>
                    </button>

                    <button
                      className="flex items-center w-full px-4 py-3 text-sm text-gray-700 hover:bg-gradient-to-r hover:from-red-100 hover:to-red-50 rounded-2xl transition-all duration-300 group"
                      onClick={() => {
                        setShowUserMenu(false);
                        signOut();
                      }}
                    >
                      <div className="w-10 h-10 bg-red-100 rounded-xl flex items-center justify-center mr-3 group-hover:scale-110 transition-transform duration-300">
                        <LogOut className="h-5 w-5 text-red-600" />
                      </div>
                      <div className="text-left">
                        <div className="font-medium">Sign Out</div>
                        <div className="text-xs text-gray-500">End your session</div>
                      </div>
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}
