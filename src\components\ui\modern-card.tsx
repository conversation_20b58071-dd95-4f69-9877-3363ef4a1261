import * as React from 'react';
import { cn } from '@/lib/utils/cn';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface ModernCardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'gradient' | 'glass' | 'elevated';
  size?: 'sm' | 'md' | 'lg';
  hover?: boolean;
  animation?: boolean;
  delay?: number;
}

const ModernCard = React.forwardRef<HTMLDivElement, ModernCardProps>(
  ({ className, variant = 'default', size = 'md', hover = true, animation = true, delay = 0, children, ...props }, ref) => {
    const variants = {
      default: 'bg-gradient-to-br from-white to-gray-50 border-0 shadow-lg',
      gradient: 'bg-gradient-to-br from-italian-green/5 via-white to-italian-red/5 border-0 shadow-xl',
      glass: 'bg-white/80 backdrop-blur-sm border border-gray-200/50 shadow-xl',
      elevated: 'bg-gradient-to-br from-white to-gray-50 border-0 shadow-2xl'
    };

    const sizes = {
      sm: 'p-4',
      md: 'p-6',
      lg: 'p-8'
    };

    const hoverEffect = hover ? 'hover:shadow-2xl hover-lift transition-all duration-500' : '';
    const animationEffect = animation ? 'animate-scale-in' : '';

    return (
      <Card
        ref={ref}
        className={cn(
          variants[variant],
          hoverEffect,
          animationEffect,
          'relative overflow-hidden',
          className
        )}
        style={animation ? { animationDelay: `${delay}ms` } : undefined}
        {...props}
      >
        {/* Background decoration */}
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-italian-green/10 to-italian-red/10 rounded-full -translate-y-16 translate-x-16"></div>
        
        <div className="relative z-10">
          {children}
        </div>
      </Card>
    );
  }
);
ModernCard.displayName = 'ModernCard';

interface ModernCardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
  icon?: React.ReactNode;
  title: string;
  subtitle?: string;
  action?: React.ReactNode;
}

const ModernCardHeader = React.forwardRef<HTMLDivElement, ModernCardHeaderProps>(
  ({ className, icon, title, subtitle, action, ...props }, ref) => {
    return (
      <CardHeader ref={ref} className={cn('pb-4', className)} {...props}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            {icon && (
              <div className="w-12 h-12 bg-gradient-to-br from-italian-green to-italian-red rounded-2xl flex items-center justify-center shadow-lg">
                {icon}
              </div>
            )}
            <div>
              <CardTitle className="text-2xl font-bold text-gray-900 italian-flag-corner">
                {title}
              </CardTitle>
              {subtitle && (
                <p className="text-gray-600 font-medium mt-1">
                  {subtitle}
                </p>
              )}
            </div>
          </div>
          {action && action}
        </div>
      </CardHeader>
    );
  }
);
ModernCardHeader.displayName = 'ModernCardHeader';

interface StatCardProps {
  title: string;
  value: string;
  change?: string;
  changePercent?: string;
  icon: React.ComponentType<{ className?: string }>;
  gradient: string;
  bgGradient: string;
  trend?: 'up' | 'down';
  description?: string;
  delay?: number;
}

const StatCard = React.forwardRef<HTMLDivElement, StatCardProps>(
  ({ title, value, change, changePercent, icon: Icon, gradient, bgGradient, trend = 'up', description, delay = 0 }, ref) => {
    return (
      <ModernCard ref={ref} variant="default" delay={delay} className="group">
        {/* Background Pattern */}
        <div className={`absolute inset-0 bg-gradient-to-br ${bgGradient} opacity-30`}></div>
        
        <CardHeader className="relative z-10 pb-4">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <CardTitle className="text-sm font-bold text-gray-600 mb-2 group-hover:text-gray-800 transition-colors duration-300">
                {title}
              </CardTitle>
              {description && (
                <p className="text-xs text-gray-500 font-medium">
                  {description}
                </p>
              )}
            </div>
            <div className={`w-16 h-16 bg-gradient-to-br ${gradient} rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 group-hover:shadow-xl transition-all duration-300`}>
              <Icon className="h-8 w-8 text-white" />
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="relative z-10 pt-0">
          <div className="space-y-4">
            <div className="text-4xl font-bold text-gray-900 group-hover:text-italian-green transition-colors duration-300">
              {value}
            </div>
            
            {(change || changePercent) && (
              <div className="flex items-center justify-between">
                {changePercent && (
                  <div className={`flex items-center px-3 py-1 rounded-full ${trend === 'up' ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`}>
                    <span className="text-sm font-bold">{changePercent}</span>
                  </div>
                )}
                {change && (
                  <div className="text-xs text-gray-500 font-medium">
                    {change}
                  </div>
                )}
              </div>
            )}
            
            {/* Progress bar */}
            {changePercent && (
              <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
                <div 
                  className={`h-full bg-gradient-to-r ${gradient} rounded-full transition-all duration-1000 ease-out`}
                  style={{ 
                    width: `${Math.min(parseInt(changePercent.replace('%', '').replace('+', '')) * 3, 100)}%`,
                    animationDelay: `${delay + 500}ms`
                  }}
                ></div>
              </div>
            )}
          </div>
        </CardContent>
      </ModernCard>
    );
  }
);
StatCard.displayName = 'StatCard';

interface ActionCardProps {
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  gradient: string;
  bgGradient: string;
  category?: string;
  href?: string;
  onClick?: () => void;
  delay?: number;
}

const ActionCard = React.forwardRef<HTMLDivElement, ActionCardProps>(
  ({ title, description, icon: Icon, gradient, bgGradient, category, href, onClick, delay = 0 }, ref) => {
    const CardWrapper = href ? 'a' : 'div';
    const cardProps = href ? { href } : { onClick };

    return (
      <CardWrapper {...cardProps} className="block group cursor-pointer">
        <div 
          ref={ref}
          className={`relative overflow-hidden bg-gradient-to-br ${bgGradient} rounded-2xl p-6 transition-all duration-500 hover:shadow-xl hover:scale-105 border-0 animate-fade-in`} 
          style={{ animationDelay: `${delay}ms` }}
        >
          {/* Background pattern */}
          <div className="absolute inset-0 bg-gradient-to-br from-white/50 to-transparent"></div>
          <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-white/20 to-transparent rounded-full -translate-y-10 translate-x-10"></div>
          
          <div className="relative z-10 flex items-center space-x-4">
            <div className={`w-16 h-16 bg-gradient-to-br ${gradient} rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 group-hover:shadow-xl transition-all duration-300`}>
              <Icon className="h-8 w-8 text-white" />
            </div>
            
            <div className="flex-1">
              {category && (
                <span className="text-xs font-bold text-gray-500 uppercase tracking-wider mb-2 block">
                  {category}
                </span>
              )}
              <h3 className="font-bold text-lg text-gray-900 group-hover:text-italian-green transition-colors duration-300 mb-1">
                {title}
              </h3>
              <p className="text-gray-600 text-sm font-medium">
                {description}
              </p>
            </div>
          </div>
          
          {/* Hover effect overlay */}
          <div className="absolute inset-0 bg-gradient-to-r from-italian-green/5 to-italian-red/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl"></div>
        </div>
      </CardWrapper>
    );
  }
);
ActionCard.displayName = 'ActionCard';

export { ModernCard, ModernCardHeader, StatCard, ActionCard };
export type { ModernCardProps, ModernCardHeaderProps, StatCardProps, ActionCardProps };
